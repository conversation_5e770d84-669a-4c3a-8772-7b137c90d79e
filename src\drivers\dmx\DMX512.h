#ifndef DMX512_H
#define DMX512_H

#include <Arduino.h>
#include <settings/settings.h>

// Forward declaration of FSEQPlayer class
class FSEQPlayer;

class DMX512
{
public:
    DMX512(FSEQPlayer *fseqPlayer) : fseqPlayer(fseqPlayer)
    {
        // while (fseqPlayer == NULL)
        //     ;
    }
    void init();
    void sendDMXData();
    void loop();
    void stop();
    void setBrightness(uint8_t level);
    uint8_t brightness;
    void turnOffChannels(int total);
    void setChannelValue(uint16_t channel, uint8_t value);

private:
    void sendBreak();
    void setRS485TransmitMode();
    void setRS485ReceiveMode();
    uint8_t dmxData[512 + 1]; // DMX data array (512 channels + start code)
    FSEQPlayer *fseqPlayer;

    // uint8_t dmxData[fseqPlayer->channelCount];
};

#endif
