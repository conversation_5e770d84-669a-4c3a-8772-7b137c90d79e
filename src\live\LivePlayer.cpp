#include "LivePlayer.h"
#include <ESPAsyncE131.h>
#include <WiFi.h>
#include <settings/settings.h>

LivePlayer::LivePlayer(
    String clientSSID,
    String clientPassword,
    String apSSID,
    String apPassword,
    IPAddress localIP,
    IPAddress gateway,
    IPAddress subnet,
    BSWiFiMode mode) : e131(1),
                       wifiMode(mode),
                       apLocalIP(localIP),
                       apGateway(gateway),
                       apSubnet(subnet),
                       apSSID(apSSID),
                       apPassword(apPassword),
                       clientSSID(clientSSID),
                       clientPassword(clientPassword)
{
    // Create DMX512 driver instance (we don't need FSEQPlayer for live mode)
    dmx = new DMX512(nullptr);
    if (!dmx)
    {
        Serial.println("ERROR: Failed to create DMX512 instance");
    }

    Serial.println("sACN E1.31 Receiver");

    // Set up WiFi based on the selected mode
    switch (wifiMode)
    {
    case BS_WIFI_MODE_CLIENT:
        setupWiFiClient(clientSSID, clientPassword);
        break;
    case BS_WIFI_MODE_AP:
        setupWiFiAP(apSSID, apPassword, apLocalIP, apGateway, apSubnet);
        break;
    case BS_WIFI_MODE_BOTH:
        setupWiFiAP(apSSID, apPassword, apLocalIP, apGateway, apSubnet);
        setupWiFiClient(clientSSID, clientPassword);
        break;
    }

    // Set hostname regardless of connection mode
    WiFi.setHostname("BS-FOUNTAIN-CONTROLLER");

    // Initialize DMX
    dmx->init();

    // Initialize E1.31 listener
    if (e131.begin(E131_UNICAST)) // Use UNICAST, or switch to MULTICAST as needed
        Serial.println("Listening for sACN data...");
    else
    {
        Serial.println("Failed to start E1.31!");
        return;
    }

    // Register callback for handling E1.31 packets
    e131.registerCallback(this, onE131Packet); // Pass `this` as UserInfo

    // Initialize timeout variables
    lastDMXPacketTime = millis();
    dmxActive = false;

    // Turn off all channels at startup
    dmx->turnOffChannels(DMX_CHANNEL_COUNT);

    Serial.println("LivePlayer initialization completed");
}

LivePlayer::~LivePlayer()
{
    Serial.println("Destroying LivePlayer");
    // Turn off all channels when destroying the player
    dmx->turnOffChannels(DMX_CHANNEL_COUNT);
    delete dmx;
}

void LivePlayer::setupWiFiClient(String ssid, String password)
{
    Serial.println("Connecting to WiFi as client...");
    WiFi.begin(ssid.c_str(), password.c_str());

    unsigned long startAttemptTime = millis();
    bool clientConnected = false;

    while (WiFi.status() != WL_CONNECTED && millis() - startAttemptTime < 15000)
    {
        delay(500);
        Serial.print(".");
    }

    if (WiFi.status() == WL_CONNECTED)
    {
        Serial.println("\nWiFi client connected!");
        Serial.print("Client IP address: ");
        Serial.println(WiFi.localIP());
    }
    else
    {
        Serial.println("\nFailed to connect as WiFi client.");
    }
}

void LivePlayer::setupWiFiAP(String ssid, String password, IPAddress localIP, IPAddress gateway, IPAddress subnet)
{
    Serial.print("Creating access point: ");
    Serial.println(ssid);

    // Configure the AP with a static IP
    if (!WiFi.softAPConfig(localIP, gateway, subnet))
    {
        Serial.println("AP IP configuration failed");
    }

    // Start the SoftAP
    if (WiFi.softAP(ssid.c_str(), password.c_str()))
    {
        Serial.println("AP started successfully");
    }
    else
    {
        Serial.println("AP failed to start");
    }

    // Print the IP address of the SoftAP
    IPAddress apIP = WiFi.softAPIP();
    Serial.print("AP IP address: ");
    Serial.println(apIP);
}

void LivePlayer::onE131Packet(e131_packet_t *packet, void *UserInfo)
{
    LivePlayer *instance = static_cast<LivePlayer *>(UserInfo);
    instance->handleE131Packet(packet); // Call instance-specific method
}

void LivePlayer::handleE131Packet(e131_packet_t *packet)
{
    // Update the last packet time
    lastDMXPacketTime = millis();
    dmxActive = true;

    uint8_t *dmxData = packet->property_values;
    uint16_t universe = ntohs(packet->universe);

    if (universe == 1)
    {
        // Process DMX data
        for (int channel = 1; channel < packet->property_value_count; channel++)
        {
            dmx->setChannelValue(channel, dmxData[channel]);
        }

        // Send the DMX data
        dmx->sendDMXData();
    }
    else if (universe == 2)
    {
        // Handle other universes if needed
    }
}

// Check if DMX timeout has occurred and turn off lights if needed
void LivePlayer::checkDMXTimeout()
{
    if (dmxActive && (millis() - lastDMXPacketTime > DMX_TIMEOUT_MS))
    {
        Serial.println("DMX timeout - turning off all channels");
        dmx->turnOffChannels(DMX_CHANNEL_COUNT);
        dmxActive = false;
    }
}

void LivePlayer::loop()
{
    // Process any pending E1.31 packets
    e131.registerCallback(this, onE131Packet);

    // Check for DMX timeout
    checkDMXTimeout();
}
