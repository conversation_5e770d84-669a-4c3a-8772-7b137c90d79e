#include <settings/settings.h>
#include "FSEQPlayer.h"

FSEQPlayer::FSEQPlayer()
{

    // Create the mutex
    xMutex = xSemaphoreCreateMutex();
    if (xMutex == NULL)
    {
        Serial.println("Failed to create mutex");
        while (1)
            ; // Halt execution if mutex creation fails
    }

    fseqFile = new File;
    dmx = new DMX512(this);

    Serial.println("Initializing FSEQPlayer...");
}

FSEQPlayer::~FSEQPlayer()
{
    // Delete the mutex
    vSemaphoreDelete(xMutex);
    delete fseqFile;
    delete dmx;
}

bool FSEQPlayer::loadFile(const char *filename, MP3Player *mp3Player)
{
    this->mp3Player = mp3Player; // Store MP3Player instance

    if (filename == NULL)
    {
        Serial.println("Error: Filename is NULL");
        return false;
    }

    if (fseqFile->available())
    {
        fseqFile->close();
    }
    Serial.println("\n - Loading FSEQ file...");

    // Open the FSEQ file
    *fseqFile = SD.open(filename, FILE_READ);

    // Read the file identifier
    fseqFile->seek(0);
    uint8_t fileIdentifier[5];
    fseqFile->read(fileIdentifier, 4);
    fileIdentifier[4] = '\0';

    if (strcmp((char *)fileIdentifier, "PSEQ") != 0)
    {
        Serial.println("Invalid FSEQ file");
        return false;
    }

    // Read additional header details

    // 4-5: Offset to start of channel data (2 bytes)
    this->offsetToChannelData = fseqFile->read() | (fseqFile->read() << 8);

    // 6: Minor version (1 byte)
    this->minorVersion = fseqFile->read();

    // 7: Major version (1 byte)
    this->majorVersion = fseqFile->read();

    // Handle different versions
    if (majorVersion == 1)
    {
        Serial.println("   Reading FSEQ v1.xx file...");
        // FSEQ v1.0 specifics
        this->standardHeaderLength = fseqFile->read() | (fseqFile->read() << 8);                                                 // 8-9
        this->channelCount = fseqFile->read() | (fseqFile->read() << 8) | (fseqFile->read() << 16) | (fseqFile->read() << 24);   // 10-13
        this->numberOfFrames = fseqFile->read() | (fseqFile->read() << 8) | (fseqFile->read() << 16) | (fseqFile->read() << 24); // 14-17
        this->stepTimeMs = fseqFile->read();                                                                                     // 18
        this->bitFlags = fseqFile->read();
        // Read universe count and universe size (bytes 20-23)
        fseqFile->seek(20);                                               // Seek to byte 20 for universe data
        this->universeCount = fseqFile->read() | (fseqFile->read() << 8); // Bytes 20-21
        this->universeSize = fseqFile->read() | (fseqFile->read() << 8);  // Bytes 22-23
        // Seek to byte 24 for gamma, color encoding, and reserved fields
        fseqFile->seek(24);
        // Read gamma value (byte 24)
        this->gamma = fseqFile->read(); // 19
        // Read color encoding (byte 25)
        this->colorEncoding = fseqFile->read();
        // Read reserved bytes (bytes 26-27)
        this->reserved = fseqFile->read() | (fseqFile->read() << 8);

        fseqFile->seek(26); // Skip to the variable headers section
    }
    else if (majorVersion == 2)
    {
        Serial.println("   Reading FSEQ v" + String(majorVersion) + ".xx" + " file...");
        // FSEQ v2.0 specifics
        this->standardHeaderLength = fseqFile->read() | (fseqFile->read() << 8);                                                 // 8-9
        this->channelCount = fseqFile->read() | (fseqFile->read() << 8) | (fseqFile->read() << 16) | (fseqFile->read() << 24);   // 10-13
        this->numberOfFrames = fseqFile->read() | (fseqFile->read() << 8) | (fseqFile->read() << 16) | (fseqFile->read() << 24); // 14-17
        this->stepTimeMs = fseqFile->read();                                                                                     // 18
        this->bitFlags = fseqFile->read();                                                                                       // 19

        // 20: Compression type and upper 4 bits of compression blocks
        this->compressionInfo = fseqFile->read();
        compressionType = compressionInfo & 0x0F;                     // Bits 0-3: Compression type
        this->compressionBlocksUpper = (compressionInfo >> 4) & 0x0F; // Bits 4-7: Upper 4 bits of compression blocks

        // 21: Lower 8 bits of compression blocks
        this->compressionBlocksLower = fseqFile->read();
        compressionBlocks = (compressionBlocksUpper << 8) | compressionBlocksLower;

        // 22: Sparse range count
        sparseRangeCount = fseqFile->read();

        // Skip reserved and 64-bit unique identifier
        fseqFile->seek(32); // Move to the variable headers section
    }
    else
    {
        Serial.println("Unsupported FSEQ version");
        return false;
    }

    // Process variable headers
    while (fseqFile->position() < offsetToChannelData)
    {
        // Read variable header length
        uint16_t variableHeaderLength = fseqFile->read() | (fseqFile->read() << 8);

        // Read header code (2 characters)
        char headerCode[3] = {0};
        fseqFile->read((uint8_t *)headerCode, 2); // Cast to uint8_t*

        headerCode[2] = '\0'; // Null-terminate the string

        if (strcmp(headerCode, "mf") == 0)
        {
            // Media Filename header
            char mediaFilename[variableHeaderLength - 2];
            fseqFile->read((uint8_t *)mediaFilename, variableHeaderLength - 2); // Cast to uint8_t*
            this->mf = mediaFilename[variableHeaderLength - 2] = '\0';          // Ensure null-termination
        }
        else if (strcmp(headerCode, "sp") == 0)
        {
            // Sequence Producer header
            char sequenceProducer[variableHeaderLength - 2];
            fseqFile->read((uint8_t *)sequenceProducer, variableHeaderLength - 2); // Cast to uint8_t*
            this->sp = sequenceProducer[variableHeaderLength - 2] = '\0';          // Ensure null-termination
        }
        else if (strcmp(headerCode, "ED") == 0)
        {
            // Extended Data (FSEQ 2.2+)
            uint64_t dataOffset = 0;
            for (int i = 0; i < 8; i++)
            {
                dataOffset |= ((uint64_t)fseqFile->read() << (i * 8));
            }
            this->dataLength = fseqFile->read() | (fseqFile->read() << 8) | (fseqFile->read() << 16) | (fseqFile->read() << 24);
        }
        else
        {
            // Unknown variable header, skip it
            fseqFile->seek(fseqFile->position() + (variableHeaderLength - 2));
        }
    }

    Serial.println("   " + String(filename));
    Serial.println("   Total channels: " + String(channelCount));
    Serial.println("   Total frames: " + String(numberOfFrames));
    Serial.println("   Offset: " + String(offsetToChannelData));
    Serial.println("   Version: " + String(majorVersion) + "." + String(minorVersion));
    Serial.println("   Step Time: " + String(stepTimeMs) + "ms");
    Serial.println("   Flags: " + String(bitFlags));
    Serial.println("   Compression: " + String(compressionType == 0 ? "none" : (compressionType == 1 ? "zstd" : (compressionType == 2 ? "zlib" : "unknown"))));
    Serial.println("   Blocks: " + String(compressionBlocks));
    Serial.println("   Sparse ranges: " + String(sparseRangeCount));
    Serial.println("   Universe Count: " + String(universeCount));
    Serial.println("   Universe Size: " + String(universeSize));
    Serial.println("   Gamma: " + String(gamma));
    Serial.println("   Color Encoding: " + String(colorEncoding));
    Serial.println("   Reserved: " + String(reserved));
    Serial.println("   Duration: " + String((numberOfFrames * stepTimeMs / 1000) / 60) + "min - (" +
                   String(numberOfFrames * stepTimeMs / 1000) + "s)");

    initialize();

    // Move to the start of channel data
    fseqFile->seek(offsetToChannelData);

    return true;
}

// Pixel LEDS show
void FSEQPlayer::loop()
{
    if (fseqFile->available())
    {
        if (mp3Player->isPlaying)
        {
            xSemaphoreTake(xMutex, portMAX_DELAY);

            unsigned long audioPosition = 0;
            // Get current audio position from MP3Player
            audioPosition = mp3Player->getAudioPlayPosition();

            // Calculate FSEQ frame based on audio position (assuming stepTimeMs is constant and represents frame duration)
            currentFrame = audioPosition / stepTimeMs;

            // Seek FSEQ to the calculated frame - this might be inefficient and needs review
            setFSEQPlayPosition(currentFrame * stepTimeMs);

            dmx->loop(); // DMX output within mutex
            xSemaphoreGive(xMutex);

            // vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(stepTimeMs));
            // vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(stepTimeMs / 2));
            // vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(1));
            // vTaskDelay(1);
        }
        else
        {
            // No audio player available, use simple timing
            xSemaphoreTake(xMutex, portMAX_DELAY);

            dmx->loop(); // DMX output within mutex
            xSemaphoreGive(xMutex);

            // Delay to synchronize with the sequence timing
            vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(stepTimeMs));
        }
    }
    else
    {
        stop();
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
}

void FSEQPlayer::initialize()
{
    dmx->init();
}

bool FSEQPlayer::hasEnded()
{
    if (fseqFile->available() == 0)
    {
        return true;
    }
    else
    {
        return false;
    }
}
void FSEQPlayer::play()
{
    isPlaying = true;
    isPaused = false;
    xLastWakeTime = xTaskGetTickCount();
}

void FSEQPlayer::pause()
{
    isPaused = true;
    isPlaying = false;
}

void FSEQPlayer::resume()
{
    isPaused = false;
    isPlaying = true;
}

void FSEQPlayer::stop()
{
    fseqFile->flush();
    fseqFile->close();
    dmx->stop();
    isPlaying = false;
}

void FSEQPlayer::setFSEQPlayPosition(int ms)
{
    if (fseqFile)
    {
        // Calculate the frame number based on the time in milliseconds
        int frameNumber = ms / stepTimeMs;
        // Calculate the byte position in the file
        int bytePosition = offsetToChannelData + (frameNumber * channelCount);
        // Seek to the calculated byte position
        fseqFile->seek(bytePosition);
    }
}

int FSEQPlayer::getFSEQPlayPosition()
{
    if (fseqFile && fseqFile->available())
    {
        // Calculate the current frame number
        long currentPosition = fseqFile->position();
        long frameNumber = (currentPosition - offsetToChannelData) / channelCount;

        // Calculate the time in milliseconds
        return frameNumber * stepTimeMs;
    }
    else
    {
        return 0; // Return 0 if the file is not open
    }
}