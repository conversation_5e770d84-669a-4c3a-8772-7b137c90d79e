#include "DMX512.h"

// Initialize the DMX communication
void DMX512::init()
{
    // Configure RS485 control pins
    pinMode(RS485_DE_PIN, OUTPUT);
    pinMode(RS485_RE_PIN, OUTPUT);

    // Set to receive mode initially
    setRS485ReceiveMode();

    // Initialize serial with DMX parameters
    Serial2.begin(250000, SERIAL_8N2, RS485_RX, RS485_TX); // DMX standard: 250kbps, 8 data bits, 2 stop bits

    // Initialize DMX data array to 0
    memset(dmxData, 0, sizeof(dmxData));
}

// Set RS485 transceiver to transmit mode
void DMX512::setRS485TransmitMode()
{
    digitalWrite(RS485_DE_PIN, HIGH); // Enable driver
    digitalWrite(RS485_RE_PIN, HIGH); // Disable receiver
    delayMicroseconds(5);             // Allow time for transceiver to switch modes
}

// Set RS485 transceiver to receive mode
void DMX512::setRS485ReceiveMode()
{
    digitalWrite(RS485_DE_PIN, LOW); // Disable driver
    digitalWrite(RS485_RE_PIN, LOW); // Enable receiver
    delayMicroseconds(5);            // Allow time for transceiver to switch modes
}

// Set a value for a specific DMX channel
void DMX512::setChannelValue(uint16_t channel, uint8_t value)
{
    if (channel >= 1 && channel <= DMXChannelCount)
    {
        dmxData[channel - 1] = value;
    }
}

// Send a DMX512 break signal (required to start a DMX frame)
void DMX512::sendBreak()
{
    setRS485TransmitMode();
    Serial2.flush();
    // Create DMX break using low TX signal
    Serial2.end(); // Temporarily disable UART
    pinMode(RS485_TX, OUTPUT);
    digitalWrite(RS485_TX, LOW);                           // Force line LOW
    delayMicroseconds(44);                                // Break: min 88 µs (100)
    digitalWrite(RS485_TX, HIGH);                          // Mark After Break
    delayMicroseconds(4);                                 // MAB: min 8 µs (12)
    Serial2.begin(250000, SERIAL_8N2, RS485_RX, RS485_TX); // Re-enable UART
}

void DMX512::sendDMXData()
{
    sendBreak();         // Send DMX break and mark-after-break
    Serial2.write(0x00); // Start code (always 0x00)

    uint16_t channelsToSend = DMXChannelCount;
    if (fseqPlayer && fseqPlayer->fseqFile && fseqPlayer->fseqFile->available())
    {
        channelsToSend = fseqPlayer->channelCount;
    }

    for (uint16_t i = 0; i < channelsToSend; i++)
    {
        Serial2.write(dmxData[i]);
        delayMicroseconds(4);
    }

    Serial2.flush();
    setRS485ReceiveMode(); // Go back to receiving mode
}

void DMX512::loop()
{
    for (int channel = 1; channel <= fseqPlayer->channelCount; channel++)
    {
        if (channel <= 512) // Handle only the first 512 channels
        {

            uint8_t value = fseqPlayer->fseqFile->read(); // Read value for the current DMX channel
            setChannelValue(channel, value);              // Set the value for the current DMX channel
        }
        else
        {
            fseqPlayer->fseqFile->read(); // Skip the remaining channels
        }
    }

    // Send the DMX data
    sendDMXData();
}

void DMX512::stop()
{
    if (fseqPlayer->channelCount >= 1)
    {

        for (int channel = 1; channel <= fseqPlayer->channelCount; channel++)
        {

            uint8_t value = 0;               // Read value for the current DMX channel
            setChannelValue(channel, value); // Set the value for the current DMX channel
        }

        // Send the DMX data
        sendDMXData();
    }
    else
    {
        turnOffChannels(512);
    }
}

void DMX512::turnOffChannels(int total)
{
    init();
    for (int channel = 1; channel <= total; channel++)
    {

        uint8_t value = 0;               // Read value for the current DMX channel
        setChannelValue(channel, value); // Set the value for the current DMX channel
    }
    sendDMXData();
}
