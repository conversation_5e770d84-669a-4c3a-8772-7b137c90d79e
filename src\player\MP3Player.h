#ifndef MP3_PLAYER_H
#define MP3_PLAYER_H

#include <Arduino.h>
#include <AudioFileSourceSD.h>
#include <AudioGeneratorMP3.h>
#include <AudioGeneratorWAV.h>
#include <AudioGeneratorFLAC.h>
#include <AudioOutputI2S.h>
#include <mutex>

class MP3Player
{
public:
    MP3Player();
    ~MP3Player();
    bool loadFile(const char *filename);
    void loop();
    void stop();
    void play();
    void pauseResume();
    void setVolume(uint8_t volume);
    bool isRunning();
    bool hasEnded();

    bool isPlaying = false;
    bool isPaused = false;
    bool isAvailable;
    bool isAudioStarted();
    void setAudioPlayPosition(int ms);
    int getAudioPlayPosition(); // New function to get audio playback position
    void clearAudioMemory();
    unsigned long startTime;

private:
    AudioGeneratorMP3 *mp3 = nullptr;
    AudioGeneratorWAV *wav = nullptr;
    AudioGeneratorFLAC *flac = nullptr;
    AudioFileSourceSD *file = nullptr;
    AudioOutputI2S *out = nullptr;
    bool isWAVFile = false;
    bool isFLACFile = false;
    bool isAudioHasInitilized = false;
    SemaphoreHandle_t audioMutex = nullptr;

    // Helper methods
    void safeStopAudio();
    void safeDeletePointers();
};

#endif
