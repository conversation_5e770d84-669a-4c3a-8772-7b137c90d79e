#include "utils.h"
#include <cctype>
#include <Arduino.h>
#include <settings/settings.h>

String lowerToUpperCase(String input)
{
    for (int i = 0; i < input.length(); i++)
    {
        input[i] = toupper(input[i]);
    }
    return input;
}

PlayMode stringToPlayMode(const String &mode)
{
    if (mode == "ASCENDING")
    {
        return ASCENDING;
    }
    else if (mode == "DESCENDING")
    {
        return DESCENDING;
    }
    else if (mode == "SHUFFLE")
    {
        return SHUFFLE;
    }
    else
    {
        // Default to SHUFFLE if mode is unrecognized
        return SHUFFLE;
    }
}

void ErrorSound()
{
    digitalWrite(buzzer, HIGH);
    vTaskDelay(buzzerHoldDelay / portTICK_PERIOD_MS);
    digitalWrite(buzzer, LOW);
}

void GlowErrorLed()
{
}

void initilizeSDCard()
{
    bool sdInitialized = false;
    int retryCount = 0;
    const int maxRetries = 5;

    Serial.println("Starting SD card initialization...");
    SPI.begin(SPI_SCK, SPI_MISO, SPI_MOSI, SD_CS_PIN);

    while (retryCount < maxRetries && !sdInitialized)
    {
        Serial.printf("Initializing SD card... (Attempt %d/%d)\n", retryCount + 1, maxRetries);

        // End any existing SD card communication
        SD.end();

        // Add delay before trying again
        delay(250);

        // Try to initialize SD card with increased timeout and reduced speed
        if (SD.begin(SD_CS_PIN, SPI, 8000000, "/sd", 5)) // speed 8MHz
        {
            sdInitialized = true;
            Serial.println("SD card initialized successfully!");

            // Verify SD card functionality
            File root = SD.open("/");
            if (root)
            {
                // Check card type and print info
                uint8_t cardType = SD.cardType();
                Serial.print("SD Card Type: ");
                if (cardType == CARD_NONE)
                {
                    Serial.println("No SD card attached");
                    sdInitialized = false;
                }
                else if (cardType == CARD_MMC)
                {
                    Serial.println("MMC");
                }
                else if (cardType == CARD_SD)
                {
                    Serial.println("SDSC");
                }
                else if (cardType == CARD_SDHC)
                {
                    Serial.println("SDHC");
                }
                else
                {
                    Serial.println("UNKNOWN");
                }

                root.close();
                break;
            }
            else
            {
                Serial.println("Failed to open root directory, retrying...");
                sdInitialized = false;
            }
        }
        else
        {
            Serial.println("SD card initialization failed, retrying...");
        }

        retryCount++;
        digitalWrite(SD_CS_PIN, HIGH); // Ensure CS is high between attempts
        delay(500);                    // Wait before next attempt
    }

    if (!sdInitialized)
    {
        Serial.println("SD card initialization failed after all attempts!");
        ErrorSound();
        ESP.restart();
    }
}

void safeCloseFile(File &file)
{
    if (file)
    {
        file.flush(); // Ensure all data is written to the SD card
        file.close();
    }
}

void pinConfigration()
{
    pinMode(buzzer, OUTPUT);

    pinMode(idlLed, OUTPUT);
    pinMode(errorLed, OUTPUT);

    // control buttons
    pinMode(nextButton, INPUT_PULLUP);
    pinMode(prevButton, INPUT_PULLUP);
    pinMode(schedulerBtn, INPUT_PULLUP);
    pinMode(enableLiveModeBtn, INPUT_PULLUP);
    pinMode(enablePlayerModeBtn, INPUT_PULLUP);
}

void checkMemoryUsage()
{
    Serial.println("===== Memory Usage =====");
    Serial.println("Free Heap: " + String(ESP.getFreeHeap()) + " bytes");
    Serial.println("Largest Free Block: " + String(ESP.getMaxAllocHeap()) + " bytes");
    Serial.println("Total Heap: " + String(ESP.getHeapSize()) + " bytes");

    if (psramFound())
    {
        Serial.println("Free PSRAM: " + String(ESP.getFreePsram()) + " bytes");
        Serial.println("Total PSRAM: " + String(ESP.getPsramSize()) + " bytes");
    }
    Serial.println("========================");
}

void periodicCheckSDCard(int milsec)
{
    // Periodic SD card check (every 30 seconds)
    static unsigned long lastSDCheck = 0;
    if (millis() - lastSDCheck > milsec) // 30000
    {
        if (!SD.exists("/"))
        {
            Serial.println("SD card error detected, reinitializing...");
            initilizeSDCard();
        }
        lastSDCheck = millis();
    }
}
