#include <Arduino.h>

void checkUnusedPins()
{
    Serial.println("\n=== UNUSED PINS ON ESP32-S3 ===");

    // Currently used pins in your project:
    // RS485: 16 (RX), 17 (TX), 4 (CONTROL)
    // SPI: 10 (CS), 11 (MOSI), 12 (SCK), 13 (MISO)
    // I2S: 38 (DOUT), 39 (BCLK), 40 (LRC)
    // I2C: 8 (SDA), 9 (SCL)
    // RTC: 41 (CLK), 42 (DAT), 14 (RST)
    // Buttons/LEDs: 3 (nextButton), 4 (prevButton), 5 (buzzer), 6 (successLED), 7 (warningLED), 15 (failLED)

    // Available GPIO pins on ESP32-S3:
    Serial.println("Available GPIO pins:");

    // GPIO 0: Boot mode selection (can be used with caution)
    Serial.println("GPIO 0 - Boot mode selection (use with caution)");

    // GPIO 1, 2: Available
    Serial.println("GPIO 1 - Available");                               // used for nextButton
    Serial.println("GPIO 2 - Available (good for WS2812 LED control)"); // used for prevButton

    // GPIO 18, 19, 20, 21: Available
    Serial.println("GPIO 18 - Available");
    Serial.println("GPIO 19 - Available");
    Serial.println("GPIO 20 - Available");
    Serial.println("GPIO 21 - Available");

    // GPIO 35, 36, 37: Available
    Serial.println("GPIO 35 - Available"); // No usable in input or output
    Serial.println("GPIO 36 - Available"); // No usable in input or output
    Serial.println("GPIO 37 - Available"); // No usable in input or output

    // GPIO 43, 44: Available (can be used for UART)
    Serial.println("GPIO 43 - Available (can be used for UART TX)");
    Serial.println("GPIO 44 - Available (can be used for UART RX)");

    // GPIO 45, 46: Strapping pins (can be used with caution)
    Serial.println("GPIO 45 - Strapping pin (use with caution)");
    Serial.println("GPIO 46 - Strapping pin (use with caution)");

    // GPIO 47, 48: Available
    Serial.println("GPIO 47 - Available"); // used for startButton
    Serial.println("GPIO 48 - Available"); // used for stopButton

    Serial.println("\nNotes:");
    Serial.println("1. GPIO 0, 45, 46 are strapping pins that affect boot mode");
    Serial.println("2. Some pins may be connected to internal flash on your specific board");
    Serial.println("3. GPIO 26-32 are not available on ESP32-S3");
    Serial.println("4. Pin 4 is used for both RS485_CONTROL_PIN and prevButton (conflict!)");
    Serial.println("================================");
}