#ifndef SETTINGS_H
#define SETTINGS_H
class MediaManager;
#include <Arduino.h>
#include <player/MediaManager.h>
#include <vector>
#include <virtuabotixRTC.h>

// DMX
extern uint16_t DMXChannelCount;

// Button and LED settings
extern uint8_t idlLed;
extern uint8_t errorLed;

extern uint8_t prevButton;
extern uint8_t nextButton;

extern uint8_t schedulerBtn;
extern uint8_t enableLiveModeBtn;
extern uint8_t enablePlayerModeBtn;

// Buzzer settings
extern uint8_t buzzer;
extern int buzzerHoldDelay;

extern bool isDebugMode;
extern uint8_t controllerVolume;
extern int delayOfSound;
extern bool isAudioHasInitilized;

// Scheduler settings
extern bool isShuedulerEnabled;

// Schedule settings
extern int scheduleHour;
extern int scheduleMinute;
extern int scheduleSecond;
extern int scheduleDay;
extern int scheduleMonth;
extern int scheduleYear;

struct TimeRange
{
    int startHour;
    int startMinute;
    int startSecond;
    int stopHour;
    int stopMinute;
    int stopSecond;
};

struct Schedule
{
    int dayOfWeek; // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    std::vector<TimeRange> timeRanges;
};

extern std::vector<Schedule> schedules;

// RS485 settings
extern uint8_t RS485_TX;
extern uint8_t RS485_RX;
extern uint8_t RS485_DE_PIN;
extern uint8_t RS485_RE_PIN;
// SPI settings
extern uint8_t SD_CS_PIN;
extern uint8_t SPI_MOSI;
extern uint8_t SPI_MISO;
extern uint8_t SPI_SCK;

// timer settings
extern uint8_t DAT; // DAT
extern uint8_t CLK; // CLK
extern uint8_t RST; // RST

// I2S settings
extern uint8_t I2S_DOUT;
extern uint8_t I2S_BCLK;
extern uint8_t I2S_LRC;

extern bool isAudioHasInitilized;

// DMX512 Settings
extern bool isDMXOutPutEnabled;

extern virtuabotixRTC RTC;

bool loadSettings(MediaManager *mediaManager);
bool saveSettings(const char *filename);
void ErrorSound();
void setRTCTime(int year, int month, int day, int hour, int minute, int second, int dayOfWeek);

extern bool resumable;

#endif // SETTINGS_H
