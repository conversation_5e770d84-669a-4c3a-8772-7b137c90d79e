#include "MP3Player.h"
#include <settings/settings.h>

MP3Player::MP3Player()
{
    Serial.println("Initializing MP3 Player...");
    isAudioHasInitilized = false;
    isPlaying = false;
    isPaused = false;
    isAvailable = true;
    startTime = 0;

    // Create mutex for thread safety
    audioMutex = xSemaphoreCreateMutex();
    if (audioMutex == NULL)
    {
        Serial.println("ERROR: Failed to create audio mutex");
    }

    // Initialize audio output
    out = new AudioOutputI2S();
    if (out)
    {
        out->SetPinout(I2S_BCLK, I2S_LRC, I2S_DOUT);
        out->SetGain(4.0); // Set initial gain
    }
    else
    {
        Serial.println("ERROR: Failed to create AudioOutputI2S");
    }
}

MP3Player::~MP3Player()
{
    clearAudioMemory();
    if (audioMutex)
    {
        vSemaphoreDelete(audioMutex);
    }
}

bool MP3Player::loadFile(const char *filename)
{
    if (xSemaphoreTake(audioMutex, pdMS_TO_TICKS(1000)) != pdTRUE)
    {
        Serial.println("ERROR: Failed to take audio mutex in loadFile");
        return false;
    }

    // Clear previous audio resources
    clearAudioMemory();

    if (filename == NULL)
    {
        Serial.println("Error: Filename is NULL");
        xSemaphoreGive(audioMutex);
        return false;
    }

    // Create new file source
    try
    {
        file = new AudioFileSourceSD(filename);
        if (!file)
        {
            Serial.println("ERROR: Failed to create AudioFileSourceSD");
            xSemaphoreGive(audioMutex);
            return false;
        }

        String filenameStr = String(filename);
        if (filenameStr.endsWith(".mp3"))
        {
            mp3 = new AudioGeneratorMP3();
            if (!mp3)
            {
                Serial.println("ERROR: Failed to create AudioGeneratorMP3");
                delete file;
                file = nullptr;
                xSemaphoreGive(audioMutex);
                return false;
            }

            if (!mp3->begin(file, out))
            {
                Serial.println("ERROR: Failed to begin MP3 playback");
                delete mp3;
                delete file;
                mp3 = nullptr;
                file = nullptr;
                xSemaphoreGive(audioMutex);
                return false;
            }

            isWAVFile = false;
            isFLACFile = false;
            Serial.println("\n- Loading MP3 file...");
        }
        else if (filenameStr.endsWith(".wav"))
        {
            wav = new AudioGeneratorWAV();
            if (!wav)
            {
                Serial.println("ERROR: Failed to create AudioGeneratorWAV");
                delete file;
                file = nullptr;
                xSemaphoreGive(audioMutex);
                return false;
            }

            if (!wav->begin(file, out))
            {
                Serial.println("ERROR: Failed to begin WAV playback");
                delete wav;
                delete file;
                wav = nullptr;
                file = nullptr;
                xSemaphoreGive(audioMutex);
                return false;
            }

            isWAVFile = true;
            isFLACFile = false;
            Serial.println("\n- Loading WAV file...");
        }
        else if (filenameStr.endsWith(".flac"))
        {
            flac = new AudioGeneratorFLAC();
            if (!flac)
            {
                Serial.println("ERROR: Failed to create AudioGeneratorFLAC");
                delete file;
                file = nullptr;
                xSemaphoreGive(audioMutex);
                return false;
            }

            if (!flac->begin(file, out))
            {
                Serial.println("ERROR: Failed to begin FLAC playback");
                delete flac;
                delete file;
                flac = nullptr;
                file = nullptr;
                xSemaphoreGive(audioMutex);
                return false;
            }

            isWAVFile = false;
            isFLACFile = true;
            Serial.println("\n- Loading FLAC file...");
        }
        else
        {
            Serial.println("Unsupported file format");
            delete file;
            file = nullptr;
            xSemaphoreGive(audioMutex);
            return false;
        }

        isAudioHasInitilized = true;
        xSemaphoreGive(audioMutex);
        return true;
    }
    catch (...)
    {
        Serial.println("Exception in loadFile");
        safeDeletePointers();
        xSemaphoreGive(audioMutex);
        return false;
    }
}

void MP3Player::loop()
{
    if (!isAudioHasInitilized || !isPlaying)
    {
        return;
    }

    if (xSemaphoreTake(audioMutex, pdMS_TO_TICKS(10)) != pdTRUE)
    {
        return; // Skip this iteration if we can't get the mutex quickly
    }

    try
    {
        if (isWAVFile)
        {
            if (wav && wav->isRunning())
            {
                if (!wav->loop())
                {
                    wav->stop();
                    isPlaying = false;
                }
            }
            else if (isPlaying)
            {
                isPlaying = false;
            }
        }
        else if (isFLACFile)
        {
            if (flac && flac->isRunning())
            {
                if (!flac->loop())
                {
                    flac->stop();
                    isPlaying = false;
                }
            }
            else if (isPlaying)
            {
                isPlaying = false;
            }
        }
        else
        {
            if (mp3 && mp3->isRunning())
            {
                if (!mp3->loop())
                {
                    mp3->stop();
                    isPlaying = false;
                }
            }
            else if (isPlaying)
            {
                isPlaying = false;
            }
        }
    }
    catch (...)
    {
        Serial.println("Exception in MP3Player::loop() - resetting state");
        isPlaying = false;
        isPaused = false;
    }

    xSemaphoreGive(audioMutex);
}

void MP3Player::safeStopAudio()
{
    try
    {
        if (isWAVFile && wav)
        {
            wav->stop();
        }
        else if (isFLACFile && flac)
        {
            flac->stop();
        }
        else if (mp3)
        {
            mp3->stop();
        }
    }
    catch (...)
    {
        Serial.println("Exception in safeStopAudio");
    }
}

void MP3Player::safeDeletePointers()
{
    try
    {
        if (wav)
        {
            delete wav;
            wav = nullptr;
        }
        if (flac)
        {
            delete flac;
            flac = nullptr;
        }
        if (mp3)
        {
            delete mp3;
            mp3 = nullptr;
        }
        if (file)
        {
            delete file;
            file = nullptr;
        }
    }
    catch (...)
    {
        Serial.println("Exception in safeDeletePointers");
        // Force reset pointers
        wav = nullptr;
        flac = nullptr;
        mp3 = nullptr;
        file = nullptr;
    }
}

void MP3Player::stop()
{
    if (xSemaphoreTake(audioMutex, pdMS_TO_TICKS(1000)) != pdTRUE)
    {
        Serial.println("ERROR: Failed to take audio mutex in stop");
        return;
    }

    isPaused = false;
    isPlaying = false;
    safeStopAudio();

    xSemaphoreGive(audioMutex);
}

void MP3Player::play()
{
    isPlaying = true;
    isPaused = false;
    startTime = millis(); // Record start time
}

void MP3Player::pauseResume()
{
    isPaused = !isPaused;
    isPlaying = !isPaused;
}

void MP3Player::setVolume(uint8_t volume)
{
    if (out)
    {
        out->SetGain(volume / 100.0);
    }
}

bool MP3Player::isRunning()
{
    if (!isAudioHasInitilized)
    {
        return false;
    }

    if (xSemaphoreTake(audioMutex, pdMS_TO_TICKS(10)) != pdTRUE)
    {
        return isPlaying; // Return current state if mutex unavailable
    }

    bool result = false;
    try
    {
        if (isWAVFile && wav)
        {
            result = wav->isRunning();
        }
        else if (isFLACFile && flac)
        {
            result = flac->isRunning();
        }
        else if (mp3)
        {
            result = mp3->isRunning();
        }
    }
    catch (...)
    {
        result = false;
    }

    xSemaphoreGive(audioMutex);
    return result;
}

bool MP3Player::hasEnded()
{
    return !isRunning();
}

bool MP3Player::isAudioStarted()
{
    return isRunning();
}

void MP3Player::setAudioPlayPosition(int ms)
{
    // Not implemented - would require seeking in the audio file
}

int MP3Player::getAudioPlayPosition()
{
    if (!isPlaying)
    {
        return 0;
    }
    return millis() - startTime;
}

void MP3Player::clearAudioMemory()
{
    if (!isAudioHasInitilized)
    {
        return;
    }

    safeStopAudio();
    safeDeletePointers();
    isAudioHasInitilized = false;
}
