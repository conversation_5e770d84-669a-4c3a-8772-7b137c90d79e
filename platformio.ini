; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:4d_systems_esp32s3_gen4_r8n16]
platform = espressif32
board = 4d_systems_esp32s3_gen4_r8n16
framework = arduino
lib_deps = 
	; https://github.com/schreibfaul1/AC101
	bblanchon/Arduino<PERSON><PERSON>@^7.0.0
	https://github.com/forkineye/ESPAsyncE131.git
	earlephilhower/ESP8266Audio@^2.0.0
	yveaux/AC101@^0.0.1
lib_ldf_mode = deep
monitor_speed = 115200
build_type = debug
monitor_filters = esp32_exception_decoder

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
lib_deps = 
	; https://github.com/schreibfaul1/AC101
	bblanchon/Arduino<PERSON>son@^7.0.0
	https://github.com/forkineye/ESPAsyncE131.git
	earlephilhower/ESP8266Audio@^2.0.0
	yveaux/AC101@^0.0.1
lib_ldf_mode = deep
monitor_speed = 115200
build_type = debug
monitor_filters = esp32_exception_decoder
