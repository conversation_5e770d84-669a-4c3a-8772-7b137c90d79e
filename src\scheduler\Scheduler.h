#ifndef SCHEDULER_H
#define SCHEDULER_H

#include "Arduino.h"
#include <Wire.h>
#include <virtuabotixRTC.h>
#include "../player/FSEQPlayer.h"
#include "../player/MP3Player.h"
#include "../player/MediaManager.h"

class MediaManager; // for forward declaration

class Scheduler
{
private:
    FSEQPlayer *fseqPlayer;
    MP3Player *mp3Player;
    MediaManager *mediaManager;
    String twoDigit(int number);
    void printDateTime();
    TaskHandle_t *mp3TaskHandle;
    TaskHandle_t *fseqTaskHandle;
    TaskHandle_t *mediaTaskHandle;
    bool isPlayTriggred = false;
    bool isStopTriggred = false;
    const int RTC_INIT_FLAG_ADDR = 0; // EEPROM address to store the RTC initialization flag

public:
    Scheduler(MP3Player *mp3, FSEQPlayer *fseq, MediaManager *media, TaskHandle_t &mp3TaskHandle, TaskHandle_t &fseqTaskHandle, TaskHandle_t &mediaTaskHandle) : mp3Player(mp3), fseqPlayer(fseq), mediaManager(media), mp3TaskHandle(&mp3TaskHandle), fseqTaskHandle(&fseqTaskHandle), mediaTaskHandle(&mediaTaskHandle)
    {
    }
    ~Scheduler() {};
    void setRTCTimeFromCompile(const char *date, const char *time);
    bool isRTCInitialized();
    void setRTCInitializedFlag();
    int calculateDayOfWeek(int year, int month, int day);
    void loop();
    void play();
    void stop();
    void init();
    bool isScheduledTimeToPlay();
    void handleManualMode();
    void suspendAllTasks();
    void resumeAllTasks();
    void deleteAllTasks();
};

#endif