# Configuration Documentation

This document provides an overview of the JSON configuration file used for
managing various modes, settings, and outputs in the project.

## Root Structure

```json
{
    "mode": "player",
    "live": { ... },
    "player": { ... },
    "brightness": 10,
    "bardrate": 115200,
    "outputs": { ... }
}
```

### Fields:

-   **mode**: Defines the mode of operation. Possible values:
    -   `"player"`: Operates in player mode.
    -   `"live"`: Operates in live mode, using settings from the `live` block.

## Live Settings

This block contains the settings for the live mode, where data is transmitted
using specific network credentials and protocols.

```json
"live": {
    "protocol": "E131",
    "ssid": "BLUESERVICE",
    "password": "84411300"
}
```

### Fields:

-   **protocol**: Communication protocol to be used during live mode. Example:
    `"E131"`.
-   **ssid**: WiFi SSID for live mode. Example: `"BLUESERVICE"`.
-   **password**: WiFi password for live mode. Example: `"84411300"`.

## Player Settings

The player mode settings configure how media files (audio, LEDs, etc.) are
played from a specified directory.

```json
"player": {
    "root_path": "/files/",
    "volume": 100,
    "leds_delay": 100,
    "audio_delay": 100,
    "playing_order": "ascending",
    "loop": true,
    "ignore_folders": [
        "System Volume Information"
    ]
}
```

### Fields:

-   **root_path**: The directory path where media files are stored. Example:
    `"/files/"`.
-   **volume**: Default audio volume level. Example: `100` (percentage).
-   **leds_delay**: Delay in milliseconds for LED updates. Example: `100` ms.
-   **audio_delay**: Delay in milliseconds for audio playback synchronization.
    Example: `100` ms.
-   **playing_order**: The order in which files are played. Possible values:
    -   `"ascending"`: Play files in ascending order.
    -   `"descending"`: Play files in descending order.
    -   `"shuffle"`: Shuffle play order.
-   **loop**: Boolean indicating whether playback should loop. Example: `true`.
-   **ignore_folders**: An array of folder names to ignore during playback.
    Example:
    -   `"System Volume Information"`

## Global Settings

```json
"brightness": 10,
"bardrate": 115200
```

### Fields:

-   **brightness**: Global brightness level for LEDs. Example: `10`
    (percentage).
-   **bardrate**: Communication baud rate. Example: `115200`.

## Outputs

This block defines the various output configurations, including WS28xx LEDs, I2C
boards (e.g., PCA9685), and DMX outputs.

```json
"outputs": {
    "ws28xx": { ... },
    "i2c_boards": { ... },
    "dmx": { ... }
}
```

### WS28xx Output

Settings for WS28xx-based LEDs.

```json
"ws28xx": {
    "order": "RGB",
    "channels": [0, 50],
    "gpio": 2
}
```

-   **order**: Defines the color order. Example: `"RGB"`.
-   **channels**: The range of channels assigned to this output. Example:
    `[0, 50]`.
-   **gpio**: The GPIO pin used for this output. Example: `2`.

### I2C Boards (PCA9685)

Settings for the I2C-based PCA9685 board used for controlling LEDs.

```json
"i2c_boards": {
    "pca9685": {
        "order": "RGB",
        "channels": [51, 100]
    }
}
```

-   **order**: Defines the color order. Example: `"RGB"`.
-   **channels**: The range of channels assigned to the PCA9685 board. Example:
    `[51, 100]`.

### DMX Output

Settings for DMX-based output.

```json
"dmx": {
    "order": "RGB",
    "channels": [101, 200]
}
```

-   **order**: Defines the color order. Example: `"RGB"`.
-   **channels**: The range of channels assigned to DMX. Example: `[101, 200]`.

---

This configuration provides the necessary parameters for controlling various
devices and outputs within the system, allowing for flexibility in live and
player modes.
