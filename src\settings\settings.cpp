#include "settings.h"
#include <Arduino.h>
#include <ArduinoJson.h>
#include <Wire.h>
#include <SD.h>
#include <live/LivePlayer.h>
#include <player/MediaManager.h>
#include <utils/utils.h>
#include <vector>

String rootDir;

// DMX
uint16_t DMXChannelCount;

// Button and LED settings
uint8_t idlLed;
uint8_t errorLed;

// Buzzer settings
uint8_t buzzer = 6;
int buzzerHoldDelay = 50;

// Debugging and controller settings
bool isDebugMode = false;
uint8_t controllerVolume;
int delayOfSound = 0;

// Player settings
bool resumable;

bool isManualMode = true;

bool isAudioHasInitilized = false;

// Scheduler settings
bool isShuedulerEnabled;

// Schedule settings
int scheduleHour = 0;
int scheduleMinute = 0;
int scheduleSecond = 0;
int scheduleDay = 0;
int scheduleMonth = 0;
int scheduleYear = 0;

std::vector<Schedule> schedules;

virtuabotixRTC RTC(CLK, DAT, RST);

// 0 - note for booting 0 mean boot 3.3 V normal startup

// Board detection and pin configuration
#if defined(CONFIG_IDF_TARGET_ESP32S3) || defined(ESP32S3)
// Buttons and Control
uint8_t prevButton = 1;
uint8_t nextButton = 2;

uint8_t schedulerBtn = 3;
uint8_t enableLiveModeBtn = 5;
uint8_t enablePlayerModeBtn = 7;
// 33
// 8

// ESP32-S3 pin configuration
uint8_t RS485_TX = 17;     // ESP32-S3 specific
uint8_t RS485_RX = 16;     // ESP32-S3 specific
uint8_t RS485_DE_PIN = 4;  // Driver Enable pin
uint8_t RS485_RE_PIN = 18; // Receiver Enable pin
// SPI settings for ESP32-S3
uint8_t SD_CS_PIN = 10;
uint8_t SPI_MOSI = 11;
uint8_t SPI_MISO = 13;
uint8_t SPI_SCK = 12;

// I2S settings for ESP32-S3
uint8_t I2S_DOUT = 38; // DIN
uint8_t I2S_BCLK = 39; // BCLK
uint8_t I2S_LRC = 40;  // WSEL

// RTC settings for ESP32-S3
uint8_t DAT = 42;
uint8_t CLK = 41;
uint8_t RST = 14;
// ----------------------------------------------------------------//
#else
// Original ESP32 pin configuration

// Buttons and Control
uint8_t prevButton = 2;  // not 36
uint8_t nextButton = 15; // not 39

uint8_t RS485_TX = 17;
uint8_t RS485_RX = 16;
uint8_t RS485_CONTROL_PIN = 4;

// SPI settings for ESP32
uint8_t SD_CS_PIN = 5;
uint8_t SPI_MOSI = 23;
uint8_t SPI_MISO = 19;
uint8_t SPI_SCK = 18;

// I2S settings for ESP32
uint8_t I2S_DOUT = 25;
uint8_t I2S_BCLK = 27;
uint8_t I2S_LRC = 26;

// RTC settings for ESP32
uint8_t DAT = 13;
uint8_t CLK = 12;
uint8_t RST = 14;
#endif

// DMX512 Settings
bool isDMXOutPutEnabled = true;

bool saveSettings(const char *filename)
{
    JsonDocument doc;

    // Serialize settings
    doc["mode"] = "PLAYER";
    doc["dmx_channel_count"] = 512;

    // Live mode settings
    doc["live"]["wifi_mode"] = "AP";

    // Client WiFi settings
    doc["live"]["client"]["ssid"] = "BLUESERVICE";
    doc["live"]["client"]["password"] = "BLUESERVICE";

    // AP WiFi settings
    doc["live"]["ap"]["ssid"] = "BLUESERVICE";
    doc["live"]["ap"]["password"] = "BLUESERVICE";
    doc["live"]["ap"]["local_ip"] = "***********";
    doc["live"]["ap"]["gateway"] = "***********";
    doc["live"]["ap"]["subnet"] = "*************";

    // Player settings
    doc["player"]["root_path"] = "/";
    doc["player"]["volume"] = 100;
    doc["player"]["audio_delay"] = 100;
    doc["player"]["playing_order"] = "ASCENDING";
    doc["player"]["loop"] = true;
    JsonArray ignoreFolders = doc["player"]["ignore_folders"].to<JsonArray>();
    ignoreFolders.add(".ignore");

    doc["player"]["is_scheduler_enabled"] = false;

    // Create schedule object with default values for each day
    JsonObject scheduleObj = doc["player"]["schedule"].to<JsonObject>();

    // Array of day names
    const char *days[] = {"sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"};

    // Create default schedule for each day
    for (int i = 0; i < 7; i++)
    {
        JsonArray daySchedule = scheduleObj.createNestedArray(days[i]);

        // Add default time ranges (9-12 and 14-18)
        JsonObject morning = daySchedule.createNestedObject();
        morning["start"] = "09:00";
        morning["end"] = "12:00";

        JsonObject afternoon = daySchedule.createNestedObject();
        afternoon["start"] = "14:00";
        afternoon["end"] = "18:00";
    }

    // Save JSON to file
    File file = SD.open(filename, FILE_WRITE);
    if (!file)
    {
        Serial.println("Failed to open settings file for writing");
        return false;
    }

    if (serializeJson(doc, file) == 0)
    {
        Serial.println("Failed to write to settings file");
        file.close();
        return false;
    }

    file.close();
    Serial.println("Settings saved successfully");
    return true;
}

bool loadSettings(MediaManager *mediaManager)
{
    JsonDocument doc;

    if (digitalRead(nextButton) == LOW && digitalRead(prevButton) == LOW)
    {
        // impliment save settings
        Serial.println("Saving settings...");
        // if already exist delete it
        if (SD.exists("/settings.json"))
        {
            Serial.println("settings.json already exist.");
            while (true)
                ;
        }
        saveSettings("/settings.json");
        Serial.println("Settings saved.");
        vTaskDelay(1000 / portTICK_PERIOD_MS);
        while (true)
            ;
    }

    Serial.println("Loading settings...");
    File file = SD.open("/settings.json", FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open settings file");
        return false;
    }

    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.print("Failed to parse settings file: ");
        Serial.println(error.f_str());
        return false;
    }

    // Extract and assign settings
    rootDir = doc["player"]["root_path"] | "/";
    controllerVolume = doc["player"]["volume"] | 100;
    resumable = doc["player"]["resumable"] | false;
    String controllerMode = lowerToUpperCase(doc["mode"] | "PLAYER");
    JsonArray ignoreFolders = doc["player"]["ignore_folders"];
    String playingOrder = lowerToUpperCase(doc["player"]["playing_order"] | "ASCENDING");
    bool isRepeatMode = doc["player"]["loop"] | true;

    DMXChannelCount = doc["live"]["dmx_channel_count"] | 512;
    // Client WiFi settings
    String clientSSID = doc["live"]["client"]["ssid"] | "BLUESERVICE";
    String clientPassword = doc["live"]["client"]["password"] | "BLUESERVICE";

    // AP WiFi settings
    String apSSID = doc["live"]["ap"]["ssid"] | "BLUESERVICE";
    String apPassword = doc["live"]["ap"]["password"] | "BLUESERVICE";

    // AP IP configuration
    String localIPStr = doc["live"]["ap"]["local_ip"] | "***********";
    String gatewayStr = doc["live"]["ap"]["gateway"] | "***********";
    String subnetStr = doc["live"]["ap"]["subnet"] | "*************";

    // Convert IP strings to IPAddress objects
    IPAddress localIP;
    IPAddress gateway;
    IPAddress subnet;

    if (!localIP.fromString(localIPStr))
    {
        Serial.println("Invalid local IP format, using default ***********");
        localIP = IPAddress(192, 168, 1, 1);
    }

    if (!gateway.fromString(gatewayStr))
    {
        Serial.println("Invalid gateway format, using default ***********");
        gateway = IPAddress(192, 168, 1, 1);
    }

    if (!subnet.fromString(subnetStr))
    {
        Serial.println("Invalid subnet format, using default *************");
        subnet = IPAddress(255, 255, 255, 0);
    }

    // Extract WiFi mode setting
    String wifiModeStr = lowerToUpperCase(doc["live"]["wifi_mode"] | "BOTH");
    BSWiFiMode bsWifiMode = BS_WIFI_MODE_BOTH; // Default to both

    if (wifiModeStr == "CLIENT")
    {
        bsWifiMode = BS_WIFI_MODE_CLIENT;
    }
    else if (wifiModeStr == "AP")
    {
        bsWifiMode = BS_WIFI_MODE_AP;
    }

    isShuedulerEnabled = doc["player"]["is_scheduler_enabled"] | false;

    // Extract and assign schedule settings
    JsonObject scheduleObject = doc["player"]["schedule"].as<JsonObject>();
    for (JsonPair kv : scheduleObject)
    {
        const char *day = kv.key().c_str();
        JsonArray timesArray = kv.value().as<JsonArray>();

        Schedule schedule;
        if (strcmp(day, "sunday") == 0)
        {
            schedule.dayOfWeek = 0;
        }
        else if (strcmp(day, "monday") == 0)
        {
            schedule.dayOfWeek = 1;
        }
        else if (strcmp(day, "tuesday") == 0)
        {
            schedule.dayOfWeek = 2;
        }
        else if (strcmp(day, "wednesday") == 0)
        {
            schedule.dayOfWeek = 3;
        }
        else if (strcmp(day, "thursday") == 0)
        {
            schedule.dayOfWeek = 4;
        }
        else if (strcmp(day, "friday") == 0)
        {
            schedule.dayOfWeek = 5;
        }
        else if (strcmp(day, "saturday") == 0)
        {
            schedule.dayOfWeek = 6;
        }

        for (JsonVariant timeVariant : timesArray)
        {
            JsonObject timeObject = timeVariant.as<JsonObject>();
            TimeRange timeRange;
            String startTime = timeObject["start"].as<String>();
            String endTime = timeObject["end"].as<String>();

            timeRange.startHour = startTime.substring(0, 2).toInt();
            timeRange.startMinute = startTime.substring(3, 5).toInt();
            timeRange.startSecond = 0;
            timeRange.stopHour = endTime.substring(0, 2).toInt();
            timeRange.stopMinute = endTime.substring(3, 5).toInt();
            timeRange.stopSecond = 0;

            schedule.timeRanges.push_back(timeRange);
        }

        schedules.push_back(schedule);
    }

    Serial.println("Settings loaded successfully.");

    // -------------------------------------
    // temporary settings
    ignoreFolders.add(".ignore");
    ignoreFolders.add("System Volume Information");
    ignoreFolders.add("lost+found");
    ignoreFolders.add(".Trash-1000");
    ignoreFolders.add(".recycle.bin");
    ignoreFolders.add(".fseventsd");
    ignoreFolders.add(".Spotlight-V100");
    ignoreFolders.add(".DS_Store");
    ignoreFolders.add(".Trashes");
    ignoreFolders.add(".Spotlight");
    ignoreFolders.add(".DocumentRevisions-V100");
    ignoreFolders.add(".TemporaryItems");
    ignoreFolders.add(".AppleDB");
    ignoreFolders.add(".AppleDesktop");
    ignoreFolders.add(".AppleDouble");

    isDebugMode = true;
    // controllerMode = "PLAYER";
    // isShuedulerEnabled = true;
    // DMXChannelCount = 10;

    // -------------------------------------

    // Seting controller volume
    mediaManager->mp3Player->setVolume(controllerVolume);

    if (digitalRead(nextButton) == LOW)
    {
        controllerMode = (controllerMode == "PLAYER") ? "LIVE" : "PLAYER";
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
    if (digitalRead(prevButton) == LOW)
    {
        // turn on all channels
        Serial.println("Turning on all channels...");
        mediaManager->fseqPlayer->dmx->init();
        while (true)
        {
            for (int channel = 1; channel <= 512; channel++)
            {
                mediaManager->fseqPlayer->dmx->setChannelValue(channel, 255);
            }
            mediaManager->fseqPlayer->dmx->sendDMXData();
            vTaskDelay(1000 / portTICK_PERIOD_MS);
        }
    }

    if (digitalRead(schedulerBtn) == LOW)
    {
        isShuedulerEnabled = !isShuedulerEnabled;
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }

    if (digitalRead(enableLiveModeBtn) == LOW)
    {
        controllerMode = "LIVE";
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
    else if (digitalRead(enablePlayerModeBtn) == LOW)
    {
        controllerMode = "PLAYER";
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }

    // If in LIVE mode, play only in live mode
    if (controllerMode == "LIVE")
    {
        Serial.println("Going to live mode...");
        LivePlayer livePlayer(
            clientSSID,
            clientPassword,
            apSSID,
            apPassword,
            localIP,
            gateway,
            subnet,
            bsWifiMode);

        // Start live mode loop here
        while (controllerMode == "LIVE")
        {
            livePlayer.loop();
            delay(10); // Adjust delay as needed
        }
        return true;
    }

    // Ignore specified folders
    for (JsonVariant folder : ignoreFolders)
    {
        if (!ignoreFolders.isNull())
        {
            mediaManager->addIgnoredFolder(folder.as<String>());
        }
    }

    // Set playback options
    mediaManager->setPlayMode(stringToPlayMode(playingOrder));
    mediaManager->setRepeatMode(isRepeatMode);

    // Scan directories on SD card
    // const char *rootDirChar = rootDir.c_str();
    mediaManager->scanDirectories("/");

    return true;
}

void setRTCTime(int year, int month, int day, int hour, int minute, int second, int dayOfWeek)
{
    RTC.setDS1302Time(second, minute, hour, dayOfWeek, day, month, year);
    Serial.print("RTC Time Set to: ");
    Serial.print(year);
    Serial.print("-");
    Serial.print(month);
    Serial.print("-");
    Serial.print(day);
    Serial.print(" ");
    Serial.print(hour);
    Serial.print(":");
    Serial.print(minute);
    Serial.print(":");
    Serial.print(second);
    Serial.print(" Day of Week: ");
    Serial.println(dayOfWeek);
}
