import serial
import time

# Open serial connection to CP2102 (adjust the port accordingly)
ser = serial.Serial('COM7', 115200)

def send_pixel_data(index, r, g, b):
    # Send pixel index and RGB values as bytes
    ser.write(bytearray([index, r, g, b]))

# Example: Set pixel 0 to red, pixel 1 to green, pixel 2 to blue
send_pixel_data(0, 255, 0, 0)  # Red
send_pixel_data(1, 0, 255, 0)  # Green
send_pixel_data(2, 0, 0, 255)  # Blue

time.sleep(1)  # Wait for a second

ser.close()
