#include <settings/settings.h>
#include "Scheduler.h"
#include "../utils/utils.h"
#include <EEPROM.h>

const int RTC_INIT_FLAG_ADDR = 0; // EEPROM address to store the RTC initialization flag

void Scheduler::init()
{
    Serial.println("||----------------------------------------------------------------------||");
    Serial.println("Scheduler initialized.");

    // Serial.print("Compiled Date Time: ");
    // Serial.print(__DATE__);
    // Serial.print("\t ");
    // Serial.println(__TIME__);

    if (!isRTCInitialized())
    {
        Serial.println("RTC not initialized. Setting RTC time from compile time...");
        setRTCTimeFromCompile(__DATE__, __TIME__);
        setRTCInitializedFlag();
    }

    Serial.println();
    RTC.updateTime();
    printDateTime();

    Serial.println("||----------------------------------------------------------------------||");

    if (isScheduledTimeToPlay())
    {
        play();
    }
    else
    {
        stop();
    }
}

void Scheduler::loop()
{
    if (isScheduledTimeToPlay())
    {
        play();
    }
    else
    {
        stop();
    }
    vTaskDelay(1000 / portTICK_PERIOD_MS);
}

void Scheduler::play()
{
    if (isPlayTriggred)
        return;
    Serial.println("Scheduler: [Play]");
    resumeAllTasks();
    mediaManager->loadIndex(random(0, mediaManager->directories.size()));

    isPlayTriggred = true;
    isStopTriggred = false;
}

void Scheduler::stop()
{
    if (this->isStopTriggred)
        return;

    Serial.println("Scheduler: [Stop]");
    // fseqPlayer->stop();
    // mp3Player->stop();
    mediaManager->stop();
    vTaskDelay(100 / portTICK_PERIOD_MS);
    suspendAllTasks();
    this->isStopTriggred = true;
    this->isPlayTriggred = false;
}

void Scheduler::suspendAllTasks()
{
    if (*mp3TaskHandle != NULL)
    {
        vTaskSuspend(*mp3TaskHandle);
    }
    if (*fseqTaskHandle != NULL)
    {
        vTaskSuspend(*fseqTaskHandle);
    }
    if (*mediaTaskHandle != NULL)
    {
        vTaskSuspend(*mediaTaskHandle);
    }
}
void Scheduler::resumeAllTasks()
{
    if (*mp3TaskHandle != NULL)
    {
        vTaskResume(*mp3TaskHandle);
    }
    if (*fseqTaskHandle != NULL)
    {
        vTaskResume(*fseqTaskHandle);
    }
    if (*mediaTaskHandle != NULL)
    {
        vTaskResume(*mediaTaskHandle);
    }
}

void Scheduler::deleteAllTasks()
{
    if (*mp3TaskHandle != NULL)
    {
        vTaskDelete(*mp3TaskHandle);
    }
    if (*fseqTaskHandle != NULL)
    {
        fseqPlayer->dmx->stop();
        vTaskDelete(*fseqTaskHandle);
    }
    if (*mediaTaskHandle != NULL)
    {
        vTaskDelete(*mediaTaskHandle);
    }
}

String Scheduler::twoDigit(int number)
{
    if (number < 10)
    {                                // If it's a single-digit number
        return "0" + String(number); // Add a leading zero
    }
    else
    {
        return String(number); // Otherwise, just return the number as is
    }
}

void Scheduler::printDateTime()
{
    int currentDayOfWeek = RTC.dayofweek;

    // Ensure currentDayOfWeek is within the range 0-6
    if (currentDayOfWeek < 0 || currentDayOfWeek > 6)
    {
        currentDayOfWeek = currentDayOfWeek % 7;
    }
    RTC.updateTime(); // Ensure the RTC time is updated before printing
    Serial.print("Date: ");
    Serial.print(twoDigit(RTC.dayofmonth));
    Serial.print("/");
    Serial.print(twoDigit(RTC.month));
    Serial.print("/");
    Serial.print(twoDigit(RTC.year));

    Serial.print("    ::    Time: ");
    Serial.print(twoDigit(RTC.hours)); // Display hours in 24-hour format
    Serial.print(":");
    Serial.print(twoDigit(RTC.minutes));
    Serial.print(":");
    Serial.print(twoDigit(RTC.seconds));

    Serial.print("      ::   Day of Week: ");
    Serial.println(currentDayOfWeek);
    Serial.println();
}

bool Scheduler::isScheduledTimeToPlay()
{
    RTC.updateTime();
    uint8_t currentDayOfWeek = RTC.dayofweek;

    // Ensure currentDayOfWeek is within the range 0-6
    if (currentDayOfWeek < 0 || currentDayOfWeek > 6)
    {
        currentDayOfWeek = currentDayOfWeek % 7;
    }

    // Assuming RTC.dayofweek returns 0 for Sunday, 1 for Monday, ..., 6 for Saturday
    for (const auto &schedule : schedules)
    {
        if (currentDayOfWeek == schedule.dayOfWeek)
        {
            for (const auto &timeRange : schedule.timeRanges)
            {
                if ((RTC.hours > timeRange.startHour ||
                     (RTC.hours == timeRange.startHour && RTC.minutes > timeRange.startMinute) ||
                     (RTC.hours == timeRange.startHour && RTC.minutes == timeRange.startMinute && RTC.seconds >= timeRange.startSecond)) &&
                    (RTC.hours < timeRange.stopHour ||
                     (RTC.hours == timeRange.stopHour && RTC.minutes < timeRange.stopMinute) ||
                     (RTC.hours == timeRange.stopHour && RTC.minutes == timeRange.stopMinute && RTC.seconds <= timeRange.stopSecond)))
                {
                    return true;
                }
            }
        }
    }
    return false;
}

bool Scheduler::isRTCInitialized()
{
    EEPROM.begin(512); // Initialize EEPROM with 512 bytes of space
    return EEPROM.read(RTC_INIT_FLAG_ADDR) == 1;
}

void Scheduler::setRTCInitializedFlag()
{
    EEPROM.write(RTC_INIT_FLAG_ADDR, 1);
    EEPROM.commit(); // Commit changes to EEPROM
}

void Scheduler::setRTCTimeFromCompile(const char *date, const char *time)
{
    // Parse date
    char monthStr[4];
    int day, year, hour, minute, second;
    sscanf(date, "%s %d %d", monthStr, &day, &year);

    // Convert month string to number
    int month;
    if (strcmp(monthStr, "Jan") == 0)
        month = 1;
    else if (strcmp(monthStr, "Feb") == 0)
        month = 2;
    else if (strcmp(monthStr, "Mar") == 0)
        month = 3;
    else if (strcmp(monthStr, "Apr") == 0)
        month = 4;
    else if (strcmp(monthStr, "May") == 0)
        month = 5;
    else if (strcmp(monthStr, "Jun") == 0)
        month = 6;
    else if (strcmp(monthStr, "Jul") == 0)
        month = 7;
    else if (strcmp(monthStr, "Aug") == 0)
        month = 8;
    else if (strcmp(monthStr, "Sep") == 0)
        month = 9;
    else if (strcmp(monthStr, "Oct") == 0)
        month = 10;
    else if (strcmp(monthStr, "Nov") == 0)
        month = 11;
    else if (strcmp(monthStr, "Dec") == 0)
        month = 12;

    // Parse time
    sscanf(time, "%d:%d:%d", &hour, &minute, &second);

    // Calculate day of the week
    int dayOfWeek = calculateDayOfWeek(year, month, day);

    // Ensure dayOfWeek is within the range 0-6
    if (dayOfWeek < 0 || dayOfWeek > 6)
    {
        dayOfWeek = dayOfWeek % 7;
    }

    // Set RTC time
    setRTCTime(year, month, day, hour, minute, second, dayOfWeek);
}

int Scheduler::calculateDayOfWeek(int year, int month, int day)
{
    // Zeller's Congruence algorithm to calculate day of the week
    if (month < 3)
    {
        month += 12;
        year -= 1;
    }
    int K = year % 100;
    int J = year / 100;
    int f = day + 13 * (month + 1) / 5 + K + K / 4 + J / 4 + 5 * J;
    int dayOfWeek = f % 7;

    // Adjust dayOfWeek to match 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    dayOfWeek = (dayOfWeek + 6) % 7;

    return dayOfWeek;
}