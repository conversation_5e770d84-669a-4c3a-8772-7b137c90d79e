#include <settings/settings.h>
#include <Arduino.h>
#include "player/MediaManager.h"
#include "player/MP3Player.h"
#include "player/FSEQPlayer.h"
#include "scheduler/Scheduler.h"
#include <utils/utils.h>
#include <SPI.h>
#include <SD.h>
#include <esp_task_wdt.h> // Include the watchdog timer header

// Task handles
TaskHandle_t LEDTaskHandle = NULL;
TaskHandle_t AudioTaskHandle = NULL;
TaskHandle_t MediaTaskHandle = NULL;
TaskHandle_t SchedulerTaskHandle = NULL;

// Function prototypes
void LEDTask(void *pvParameters);
void AudioTask(void *pvParameters);
void MediaTask(void *pvParameters);
void SchedulerTask(void *pvParameters);

// Objects creation
MP3Player mp3Player;
FSEQPlayer fseqPlayer;
MediaManager mediaManager(&fseqPlayer, &mp3Player);
Scheduler scheduler(&mp3Player, &fseqPlayer, &mediaManager, AudioTaskHandle, LEDTaskHandle, MediaTaskHandle);

void setup()
{
    Serial.begin(115200);
    delay(1000); // Give serial time to initialize
    Serial.println("Starting setup...");

    // Turn off all dmx channels
    fseqPlayer.dmx->stop();

    // I/O Pin Configuration and definition
    pinConfigration();

    // SD Card Initialization with retry mechanism
    initilizeSDCard();

    // For Debuging
    // setRTCTime(2025, 2, 24, 23, 53, 0, 6);
    // delay(1000);

    if (!loadSettings(&mediaManager))
    {
        Serial.println("Failed to load settings. Please check your settings.json file");
        while (1)
            ;
        delay(1000); // Add delay to prevent watchdog reset
    }

    // Create tasks and assign them to different cores
    BaseType_t xReturned;

    xReturned = xTaskCreatePinnedToCore(
        LEDTask,        // Function to implement the task
        "LED Task",     // Name of the task
        16384,          // Reduced stack size (was 32768)
        NULL,           // Task input parameter
        5,              // Highest priority for LED control
        &LEDTaskHandle, // Task handle
        0               // Run on Core 0
    );
    if (xReturned == pdPASS)
    {
        esp_task_wdt_delete(LEDTaskHandle); // Disable watchdog for LED Task
    }
    else
    {
        Serial.println("Failed to create LED Task");
    }

    xReturned = xTaskCreatePinnedToCore(
        AudioTask,        // Function to implement the task
        "Audio Task",     // Name of the task
        16384,            // Stack size
        NULL,             // Task input parameter
        4,                // High priority for audio
        &AudioTaskHandle, // Task handle
        1                 // Run on Core 1
    );
    if (xReturned == pdPASS)
    {
        esp_task_wdt_delete(AudioTaskHandle); // Disable watchdog for Audio Task
    }
    else
    {
        Serial.println("Failed to create Audio Task");
    }

    xReturned = xTaskCreatePinnedToCore(
        MediaTask,        // Function to implement the task
        "Media Task",     // Name of the task
        8192,             // Stack size
        NULL,             // Task input parameter
        3,                // Medium priority
        &MediaTaskHandle, // Task handle
        1
        // tskNO_AFFINITY    // Run on Core 0
    );
    if (xReturned == pdPASS)
    {
        esp_task_wdt_delete(MediaTaskHandle); // Disable watchdog for Media Task
    }
    else
    {
        Serial.println("Failed to create Media Task");
    }

    xReturned = xTaskCreatePinnedToCore(
        SchedulerTask,        // Function to implement the task
        "Scheduler Task",     // Name of the task
        4096,                 // Reduced stack size (was 16384)
        NULL,                 // Task input parameter
        2,                    // Lowest priority
        &SchedulerTaskHandle, // Task handle
        0                     // Run on Core 0
    );
    if (xReturned == pdPASS)
    {
        esp_task_wdt_delete(SchedulerTaskHandle); // Disable watchdog for Scheduler Task
    }
    else
    {
        Serial.println("Failed to create Scheduler Task");
    }
}

void loop()
{
    // if (digitalRead(nextButton) == HIGH && digitalRead(prevButton) == HIGH)
    // {
    //     saveSettings("/settings.json");
    // }

    // if (isShuedulerEnabled)
    // {
    //     scheduler.loop();
    // }

    vTaskDelete(NULL);
}

void LEDTask(void *pvParameters)
{
    // Add a delay to ensure all objects are initialized
    vTaskDelay(100 / portTICK_PERIOD_MS);

    // Check if fseqPlayer is properly initialized
    if (&fseqPlayer == NULL)
    {
        Serial.println("ERROR: fseqPlayer is NULL in LEDTask");
        vTaskDelete(NULL);
        return;
    }

    while (true)
    {
        try
        {
            fseqPlayer.loop();
        }
        catch (...)
        {
            // Catch any exceptions to prevent task crashes
            Serial.println("Exception in LEDTask");
        }
        vTaskDelay(1 / portTICK_PERIOD_MS);
    }
}

void AudioTask(void *pvParameters)
{
    // Add a delay to ensure all objects are initialized
    vTaskDelay(100 / portTICK_PERIOD_MS);

    // Check if mp3Player is properly initialized
    if (&mp3Player == NULL)
    {
        Serial.println("ERROR: mp3Player is NULL in AudioTask");
        vTaskDelete(NULL);
        return;
    }

    while (true)
    {
        try
        {
            mp3Player.loop();
        }
        catch (...)
        {
            // Catch any exceptions to prevent task crashes
            Serial.println("Exception in AudioTask");
        }
        vTaskDelay(1 / portTICK_PERIOD_MS);
    }
}

void MediaTask(void *pvParameters)
{
    // Add a delay to ensure all objects are initialized
    vTaskDelay(100 / portTICK_PERIOD_MS);

    // Check if mediaManager is properly initialized
    if (&mediaManager == NULL)
    {
        Serial.println("ERROR: mediaManager is NULL in MediaTask");
        vTaskDelete(NULL);
        return;
    }

    while (true)
    {
        try
        {
            mediaManager.loop();
        }
        catch (...)
        {
            // Catch any exceptions to prevent task crashes
            Serial.println("Exception in MediaTask");
        }
        vTaskDelay(1 / portTICK_PERIOD_MS);
    }
}

void SchedulerTask(void *pvParameters)
{
    // Add a delay to ensure all objects are initialized
    vTaskDelay(1 / portTICK_PERIOD_MS);

    if (!isShuedulerEnabled)
    {
        Serial.println("Scheduler is disabled, deleting SchedulerTask");
        vTaskDelete(NULL);
        return;
    }

    // Check if scheduler is properly initialized
    if (&scheduler == NULL)
    {
        Serial.println("ERROR: scheduler is NULL in SchedulerTask");
        vTaskDelete(NULL);
        return;
    }

    try
    {
        scheduler.init();
    }
    catch (...)
    {
        Serial.println("Exception in scheduler.init()");
        vTaskDelete(NULL);
        return;
    }

    while (true)
    {
        try
        {
            scheduler.loop();
        }
        catch (...)
        {
            // Catch any exceptions to prevent task crashes
            Serial.println("Exception in SchedulerTask");
        }
        vTaskDelay(10 / portTICK_PERIOD_MS);
    }
}
