#ifndef MEDIA_MANAGER_H
#define MEDIA_MANAGER_H

#include "Arduino.h"
#include <SD.h>
#include "FSEQPlayer.h"
#include "MP3Player.h"
#include "../scheduler/Scheduler.h"
#include "vector"
#include <algorithm>
#include <random>
#include <chrono>

enum PlayMode
{
    ASCENDING,
    DESCENDING,
    SHUFFLE
};

class MediaManager
{
private:
    PlayMode currentMode;
    size_t currentDirectoryIndex = 0;
    bool mediaPlaying = false;
    bool isRepeatMode = false;
    unsigned long previousMillis = 0;
    unsigned long lastDebounceTime = 0;
    unsigned long debounceDelay = 1000; // milliseconds

    // Helper function to shuffle a vector
    template <typename T>
    void shuffleVector(std::vector<T> &vec)
    {
        for (int i = vec.size() - 1; i > 0; --i)
        {
            int j = random(0, i + 1);
            std::swap(vec[i], vec[j]);
        }
    }

public:
    std::vector<String> directories;
    std::vector<String> ignoreFolders;
    MediaManager(FSEQPlayer *fseq, MP3Player *mp3) : fseqPlayer(fseq), mp3Player(mp3), currentMode(ASCENDING) {}

    FSEQPlayer *fseqPlayer;
    MP3Player *mp3Player;
    bool fseqLoaded = false;
    bool mp3Loaded = false;
    // Set the play mode
    void setPlayMode(PlayMode mode)
    {
        currentMode = mode;
    }

    void setRepeatMode(bool mode)
    {
        isRepeatMode = mode;
    }

    // Check if the folder should be ignored
    bool isIgnored(const String &folder);

    // Add folders to the ignore list
    void addIgnoredFolder(const String &folderName);

    // Scan for all directories in the root of the SD card
    void scanDirectories(const char *dirPath);

    // Get all media files (.fseq, .mp3, and .flac) in a given directory
    std::vector<String> getMediaFiles(const char *dirPath, const char *extension);

    // Play all media files from the directory in the chosen play mode
    void playMediaFromDirectory(const char *dirPath);

    // Sort directories based on the current play mode
    void sortDirectories();

    // Sort files based on the current play mode
    void sortFiles(std::vector<String> &files);

    void mediaController();

    // maintain loop
    void loop();

    void next();
    void prev();
    void loadIndex(int index);
    void stop();
    void play();
    // void pause();
    // void resume();
    // void play();
};

#endif