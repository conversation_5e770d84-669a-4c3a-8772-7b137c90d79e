#ifndef FSEQ_PLAYER_H
#define FSEQ_PLAYER_H

#include <SD.h>
#include "../drivers/dmx/DMX512.h"

class DMX512;    // forward declaration
class MP3Player; // forward declaration
class FSEQPlayer
{
public:
    FSEQPlayer();
    ~FSEQPlayer();
    bool loadFile(const char *filename, MP3Player *mp3Player); // Modified to accept MP3Player instance
    void loop();
    void play();
    void pause();
    void resume();
    void stop();
    bool hasEnded();
    void setFSEQPlayPosition(int ms);
    int getFSEQPlayPosition();
    bool isPlaying = false;
    bool isPaused = false;
    bool readyToPlay;
    TickType_t xLastWakeTime;
    SemaphoreHandle_t xMutex;
    File *fseqFile;
    DMX512 *dmx;
    MP3Player *mp3Player; // Add MP3Player member
    void initialize();

public:
    uint32_t currentFrame;
    // static constexpr uint8_t dataPin = 2;
    // v1
    uint16_t offsetToChannelData = 0;
    uint8_t minorVersion;
    uint8_t majorVersion;
    uint16_t standardHeaderLength;
    uint32_t channelCount;
    uint32_t numberOfFrames;
    uint8_t stepTimeMs;
    uint8_t bitFlags;
    uint8_t compressionType;
    uint16_t compressionBlocks;
    uint8_t sparseRangeCount;
    uint16_t universeCount;
    uint16_t universeSize;
    uint8_t gamma;
    uint8_t colorEncoding;
    uint16_t reserved;

    // v1.0+
    String mf;
    String sp;
    uint32_t dataLength;

    // v2
    uint8_t compressionInfo;
    uint8_t compressionBlocksUpper;
    uint8_t compressionBlocksLower;
    // v2.2

    void initializeRS485DMX();
    void sendDMXFrame(uint8_t *data, int channelCount);

    // Check if audio and FSEQ are still in sync
    bool checkSyncStatus();

    // Force resynchronization with audio
    void forceResync();
};

#endif
