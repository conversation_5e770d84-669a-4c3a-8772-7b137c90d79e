#ifndef LIVEPLAYER_H
#define LIVEPLAYER_H

#include <ESPAsyncE131.h>
#include <drivers/dmx/DMX512.h>

#define DMX_CHANNEL_COUNT 512
#define DMX_TIMEOUT_MS 5000 // 5 seconds timeout for DMX data

// Renamed to avoid conflict with ESP32's wifi_mode_t
enum BSWiFiMode
{
    BS_WIFI_MODE_CLIENT, // Connect to existing WiFi network
    BS_WIFI_MODE_AP,     // Create an access point
    BS_WIFI_MODE_BOTH    // Both client and AP modes
};

class LivePlayer
{
public:
    LivePlayer(
        String clientSSID,
        String clientPassword,
        String apSSID,
        String apPassword,
        IPAddress localIP = IPAddress(192, 168, 1, 1),
        IPAddress gateway = IPAddress(192, 168, 1, 1),
        IPAddress subnet = IPAddress(255, 255, 255, 0),
        BSWiFiMode mode = BS_WIFI_MODE_BOTH);
    ~LivePlayer();

    void loop();

private:
    static void onE131Packet(e131_packet_t *packet, void *UserInfo);
    void handleE131Packet(e131_packet_t *packet);
    void checkDMXTimeout();

    // WiFi setup methods
    void setupWiFiClient(String ssid, String password);
    void setupWiFiAP(String ssid, String password, IPAddress localIP, IPAddress gateway, IPAddress subnet);

    ESPAsyncE131 e131;
    DMX512 *dmx; // Use the DMX512 driver instead of reimplementing

    // WiFi configuration
    BSWiFiMode wifiMode;
    IPAddress apLocalIP;
    IPAddress apGateway;
    IPAddress apSubnet;
    String apSSID;
    String apPassword;
    String clientSSID;
    String clientPassword;

    // DMX state tracking
    unsigned long lastDMXPacketTime;
    bool dmxActive;
};

#endif
