#include <settings/settings.h>
#include "MediaManager.h"
#include <utils/utils.h>

// Check if the folder should be ignored
bool MediaManager::isIgnored(const String &folder)
{
    for (const auto &ignored : ignoreFolders)
    {
        if (folder.equalsIgnoreCase(ignored))
        {
            return true;
        }
    }
    return false;
}
void MediaManager::sortDirectories()
{
    if (currentMode == ASCENDING)
    {
        std::sort(directories.begin(), directories.end()); // Sort alphabetically
    }
    else if (currentMode == DESCENDING)
    {
        std::sort(directories.rbegin(), directories.rend()); // Sort in reverse alphabetical order
    }
    else if (currentMode == SHUFFLE)
    {
        // Seed using a high-resolution clock if random_device isn't available
        auto seed = std::chrono::high_resolution_clock::now().time_since_epoch().count();
        std::mt19937 g(seed); // <PERSON><PERSON><PERSON>wi<PERSON> seeded with current time

        // Shuffle the directories vector
        std::shuffle(directories.begin(), directories.end(), g);
    }
}

// Add folders to the ignore list
void MediaManager::addIgnoredFolder(const String &folderName)
{
    ignoreFolders.push_back(folderName);
}

// Scan for all directories in the root of the SD card, excluding ignored folders
void MediaManager::scanDirectories(const char *dirPath)
{
    File root = SD.open(dirPath);
    if (!root)
    {
        Serial.println("Failed to open root directory!");
        return;
    }

    while (true)
    {
        File entry = root.openNextFile();
        if (!entry)
            break;

        if (entry.isDirectory())
        {
            String folderName = String(entry.name());
            if (!isIgnored(folderName))
            {
                directories.push_back(folderName);
                Serial.println("Found directory: " + folderName);
            }
            else
            {
                Serial.println("Ignoring directory: " + folderName);
            }
        }
        entry.close();
    }
    root.close(); // Close the root directory after scan
    // Sort directories based on the current play mode
    sortDirectories();
}

// Get all media files (.fseq, .mp3, .wav, and .flac) in a given directory
std::vector<String> MediaManager::getMediaFiles(const char *dirPath, const char *extension)
{
    std::vector<String> files;
    File dir = SD.open(dirPath);
    if (!dir)
    {
        Serial.print("Failed to open directory: ");
        Serial.println(dirPath);
        return files;
    }

    while (true)
    {
        File entry = dir.openNextFile();
        if (!entry)
            break;

        if (!entry.isDirectory())
        {
            String fileName = String(entry.name());
            if (fileName.endsWith(extension))
            {
                files.push_back(fileName);
            }
        }
        safeCloseFile(entry); // Use the safe close method instead of entry.close()
    }

    safeCloseFile(dir); // Use the safe close method instead of dir.close()
    return files;
}

// Play media files from the given directory
void MediaManager::playMediaFromDirectory(const char *dirPath)
{
    stop();
    std::vector<String> fseqFiles = getMediaFiles(dirPath, ".fseq");
    std::vector<String> mp3Files = getMediaFiles(dirPath, ".mp3");
    std::vector<String> wavFiles = getMediaFiles(dirPath, ".wav");
    std::vector<String> flacFiles = getMediaFiles(dirPath, ".flac");

    if (fseqFiles.empty() && mp3Files.empty() && wavFiles.empty() && flacFiles.empty())
    {
        Serial.println("No media files found in directory.");
        return;
    }

    // Load FSEQ and MP3/WAV/FLAC files before starting playback
    if (!fseqFiles.empty())
    {
        String fseqFilePath = String(dirPath) + "/" + fseqFiles[0];
        fseqLoaded = fseqPlayer->loadFile(fseqFilePath.c_str(), mp3Player); // Pass mp3Player instance
        Serial.print("FSEQ file loaded: ");
        Serial.println(fseqFilePath);
    }
    else
    {
        fseqLoaded = false;
    }

    if (!mp3Files.empty())
    {
        String mp3FilePath = String(dirPath) + "/" + mp3Files[0];
        mp3Loaded = mp3Player->loadFile(mp3FilePath.c_str());
        Serial.print("MP3 file loaded: ");
        Serial.println(mp3FilePath);
    }
    else if (!wavFiles.empty())
    {
        String wavFilePath = String(dirPath) + "/" + wavFiles[0];
        mp3Loaded = mp3Player->loadFile(wavFilePath.c_str());
        Serial.print("WAV file loaded: ");
        Serial.println(wavFilePath);
    }
    else if (!flacFiles.empty())
    {
        String flacFilePath = String(dirPath) + "/" + flacFiles[0];
        mp3Loaded = mp3Player->loadFile(flacFilePath.c_str());
        Serial.print("FLAC file loaded: ");
        Serial.println(flacFilePath);
    }
    else
    {
        mp3Loaded = false;
    }

    // Ensure both are loaded before playing
    if (fseqLoaded && mp3Loaded)
    {
        Serial.println("||----------------------------------------------------------------------||\n");
        // Serial.println("Starting synchronized playback...");
        // vTaskDelay(pdMS_TO_TICKS(400)); // Introduce a small delay (50ms)
        // vTaskDelay(pdMS_TO_TICKS(300));     // Introduce a small delay (50ms)
        // while (!fseqPlayer->fseqFileLoaded)
        // {
        //     vTaskDelay(pdMS_TO_TICKS(10));
        // }
        fseqPlayer->play(); // Start FSEQ playback first
        mp3Player->play();  // Start MP3/WAV/FLAC playback
        // if (fseqPlayer->fseqFileLoaded)
        // {

        //     mp3Player->setAudioPlayPosition(0); // Start MP3/WAV/FLAC from the beginning
        //     fseqPlayer->setFSEQPlayPosition(0); // Start FSEQ from the beginning
        //     fseqPlayer->play();                 // Start FSEQ playback first
        //     mp3Player->play();                  // Start MP3/WAV/FLAC playback
        // }
        // else
        // {
        //     while (!fseqPlayer->fseqFileLoaded)
        //     {
        //         vTaskDelay(pdMS_TO_TICKS(100));
        //     }
        //     mp3Player->setAudioPlayPosition(0); // Start MP3/WAV/FLAC from the beginning
        //     fseqPlayer->setFSEQPlayPosition(0); // Start FSEQ from the beginning
        //     fseqPlayer->play();                 // Start FSEQ playback first
        //     mp3Player->play();                  // Start MP3/WAV/FLAC playback
        // }
    }
    else if (fseqLoaded)
    {
        Serial.println("||----------------------------------------------------------------------||");
        Serial.println("\n Starting FSEQ playback only...");
        fseqPlayer->play();
    }
    else if (mp3Loaded)
    {
        Serial.println("||----------------------------------------------------------------------||");
        Serial.println("\n Starting MP3/WAV/FLAC playback only...");
        mp3Player->play();
    }
    else
    {
        Serial.println("No media files loaded for playback.");
    }
}

// Maintain media control using loop
void MediaManager::loop()
{
    periodicCheckSDCard(30000);
    mediaController();

    // buzzersound();
    ErrorSound();

    // if (digitalRead(rotatorAuto) == LOW)
    // {
    //     Serial.println("rotatorAuto pressed");
    // }
    // if (digitalRead(rotatorStatic) == LOW)
    // {
    //     Serial.println("rotatorStatic pressed");
    // }
    // if (digitalRead(rotatorManual) == LOW)
    // {
    //     Serial.println("rotatorManual pressed");
    // }
}

// Media controller to manage current media and transitions
void MediaManager::mediaController()
{
    if (!mediaPlaying)
    {
        // If the current media has ended, move to the next directory
        if (currentDirectoryIndex < directories.size())
        {
            // Play all media from the current directory
            String currentDirectory = directories[currentDirectoryIndex];
            String dirPath = "/" + currentDirectory;
            Serial.print("\n\n||----------------------------------------------------------------------||");
            Serial.println("\n => Playing media from directory: " + dirPath);

            // Play media from the current directory
            this->playMediaFromDirectory(dirPath.c_str());

            mediaPlaying = true; // Media is now playing
        }
    }

    // Check if media has ended and move to the next directory based on the loaded files
    if (mp3Loaded && fseqLoaded)
    {
        // Case 1: Both MP3 and FSEQ are loaded, move to the next only if both have ended
        if (mp3Player->hasEnded() && fseqPlayer->hasEnded())
        {
            currentDirectoryIndex++;
            mediaPlaying = false; // Ready to play next media
        }
    }
    else if (mp3Loaded && !fseqLoaded)
    {
        // Case 2: Only MP3 is loaded and has ended, but also ensure FSEQ is not mistakenly loaded
        if (mp3Player->hasEnded())
        {
            Serial.println("mp3 has ended");
            currentDirectoryIndex++;
            mediaPlaying = false; // Ready to play next media
        }
    }
    else if (!mp3Loaded && fseqLoaded)
    {
        // Case 3: Only FSEQ is loaded, move to the next if FSEQ has ended
        if (fseqPlayer->hasEnded())
        {
            currentDirectoryIndex++;
            mediaPlaying = false; // Ready to play next media
        }
    }
    else
    {
        // Case 4: Neither MP3 nor FSEQ are loaded
        Serial.println("No media loaded, cannot proceed.");
        vTaskDelay(pdMS_TO_TICKS(1000));
        mediaPlaying = false; // Ready to play next media
    }

    // Optionally, loop back to the first directory when all have been played
    if ((currentDirectoryIndex >= directories.size()) && isRepeatMode)
    {
        currentDirectoryIndex = 0; // Loop back to the first directory
    }

    // Handle 'next' button
    if (digitalRead(nextButton) == LOW)
    {
        if ((millis() - lastDebounceTime) > debounceDelay)
        {
            if (digitalRead(nextButton) == LOW)
            {
                this->next();
                lastDebounceTime = millis();
            }
        }
    }

    // Handle 'previous' button
    if (digitalRead(prevButton) == LOW)
    {
        if ((millis() - lastDebounceTime) > debounceDelay)
        {
            if (digitalRead(prevButton) == LOW)
            {
                this->prev();
                lastDebounceTime = millis();
            }
        }
    }
}

void MediaManager::next()
{

    digitalWrite(buzzer, HIGH);
    Serial.println("\n\n\t - NEXT -\n");
    if (currentDirectoryIndex >= directories.size() - 1)
    {
        currentDirectoryIndex = 0;
    }
    else
    {
        currentDirectoryIndex++;
    }

    mediaPlaying = false;

    vTaskDelay(buzzerHoldDelay / portTICK_PERIOD_MS);
    digitalWrite(buzzer, LOW);
}

void MediaManager::prev()
{
    digitalWrite(buzzer, HIGH);
    Serial.println("\n\t - PREV -\n");
    if (currentDirectoryIndex == 0)
    {
        currentDirectoryIndex = directories.size() - 1;
    }
    else
    {
        currentDirectoryIndex--;
    }
    mediaPlaying = false;

    vTaskDelay(buzzerHoldDelay / portTICK_PERIOD_MS);
    digitalWrite(buzzer, LOW);
}

void MediaManager::loadIndex(int index)
{
    currentDirectoryIndex = index;
    mediaPlaying = false;
}

void MediaManager::stop()
{
    if (fseqPlayer->isPlaying)
    {
        fseqPlayer->stop();
    }
    if (mp3Player->isPlaying)
    {
        mp3Player->stop();
    }
}
void MediaManager::play()
{
    fseqPlayer->play(); // Start FSEQ first to prioritize lights
    mp3Player->play();  // Then start audio immediately after
}